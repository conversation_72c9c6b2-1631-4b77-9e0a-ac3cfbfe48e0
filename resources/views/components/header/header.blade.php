@aware(['page'])
<header x-data="Header" data-module="ds-header" {{ $attributes->merge(['class' => 'ds-header']) }} x-cloak>
    <nav class="flex items-center justify-between ds-site-padding space-x-6">
        <div class="flex lg:flex-shrink-0">
            <a href="#home" class="-m-1.5 p-1.5 logo">
                <span class="sr-only">Le Flamant</span>
                <img class="master-logo" src="/img/le-flamant-wordmark.svg" alt="Le Flamant">
            </a>
        </div>

        <!-- Desktop Menu -->
        <ul class="hidden lg:flex lg:space-x-6 xl:space-x-10 desktop-menu">
            <li class="li group">
                <a href="#home" class="nav-link" data-target="home">Home
                <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#about" class="nav-link" data-target="about">
                    About Us
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#food" class="nav-link" data-target="food">
                    Our Food
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#drinks" class="nav-link" data-target="drinks">
                    Our Drinks
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#gallery" class="nav-link" data-target="gallery">
                    Gallery
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#contact" class="nav-link" data-target="contact">Contact
                <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
        </ul>

        <div class="flex items-center space-x-6">
            <x-button data-fancybox="reservations" data-src="#reservation-modal" data-type="inline" type="secondary" class="!hidden md:!block header-btn">Book a table</x-button>
            <x-hamburger-icon class="lg:hidden" />
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div @keydown.window.escape="closeMenu()" x-show="menuopen" x-cloak
    class="fixed inset-0 z-50 overflow-hidden h-dvh lg:!hidden" aria-labelledby="slide-over-title" x-ref="dialog"
    aria-modal="true">
        <div class="absolute inset-0 overflow-hidden">
            <div x-transition:enter="ease-in-out duration-500" x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-500"
                x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                x-description="Background overlay, show/hide based on slide-over state."
                class="absolute inset-0 transition-opacity bg-black bg-opacity-70" x-on:click="menuopen = false"
                aria-hidden="true">
            </div>
            <div class="absolute inset-y-0 right-0 flex max-w-full md:pl-10">
                <div x-show="menuopen"
                    x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
                    x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full"
                    class="w-screen max-w-full md:max-w-md"
                    x-description="Slide-over panel, show/hide based on slide-over state.">
                    <div class="flex flex-col h-full pt-6 pb-10 overflow-y-scroll shadow-xl bg-sage-500 md:pb-16">
                        <div class="relative flex-1 px-4 md:mt-24 sm:px-6">
                            <div class="absolute inset-0 flex flex-col justify-between px-4 sm:px-8">
                                <nav aria-label="Sidebar" class="flex flex-col h-full mobile-menu">
                                    <a href="#home" class="md:hidden">
                                        <span class="sr-only">Le Flamant</span>
                                        <img class="master-logo" src="/img/le-flamant-wordmark.svg" alt="Le Flamant">
                                    </a>
                                   <ul x-data="{ selected: null }">
                                        <li><a href="#home" class="group" @click="closeMenu()">Home</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#about" class="group" @click="closeMenu()">About Us</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#food" class="group" @click="closeMenu()">Our Food</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#drinks" class="group" @click="closeMenu()">Our Drinks</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#gallery" class="group" @click="closeMenu()">Gallery</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#contact" class="group" @click="closeMenu()">Contact</a></li>
                                        <div class="divider"></div>
                                    </ul>

                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>