9999999999O:22:"Statamic\Entries\Entry":19:{s:5:" * id";s:4:"home";s:13:" * collection";s:5:"pages";s:12:" * blueprint";s:5:"pages";s:7:" * date";N;s:9:" * locale";s:7:"default";s:21:" * afterSaveCallbacks";a:0:{}s:13:" * withEvents";b:1;s:11:" * template";N;s:9:" * layout";N;s:19:" * withComputedData";b:1;s:7:" * data";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:7:{s:9:"blueprint";s:5:"pages";s:5:"title";s:4:"Home";s:8:"template";s:15:"pages/home/<USER>";s:6:"author";s:36:"8427a0f1-0dc6-4a13-868d-d689693cbda7";s:10:"updated_by";s:36:"8427a0f1-0dc6-4a13-868d-d689693cbda7";s:10:"updated_at";i:1739198448;s:7:"content";s:705:"## Welcome to your brand new Statamic site!

Not sure where to do next? Here are a few ideas, but feel free to explore in your own way, in your own time.

- [Jump into the Control Panel](/cp) and edit this page or begin setting up your own collections and blueprints.
- [Head to the docs](https://statamic.dev) and learn how Statamic works.
- [Watch some Statamic videos](https://youtube.com/statamic) on YouTube.
- [Join our Discord chat](https://statamic.com/discord) and meet thousands of other Statamic developers.
- [Start a discussion](https://github.com/statamic/cms/discussions) and get answers to your questions.
- [Star Statamic on Github](https://github.com/statamic/cms) if you enjoy using it!";}s:28:" * escapeWhenCastingToString";b:0;}s:14:" * supplements";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:14:" * initialPath";s:97:"/Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/content/collections/pages/home.md";s:12:" * published";b:1;s:23:" * selectedQueryColumns";N;s:25:" * selectedQueryRelations";a:0:{}s:11:" * original";a:0:{}s:9:" * origin";N;s:7:" * slug";s:4:"home";}