9999999999O:22:"Statamic\Entries\Entry":19:{s:5:" * id";s:36:"0fe2989c-a626-4208-93bf-0afa62fdcc73";s:13:" * collection";s:5:"pages";s:12:" * blueprint";s:4:"page";s:7:" * date";N;s:9:" * locale";s:7:"default";s:21:" * afterSaveCallbacks";a:0:{}s:13:" * withEvents";b:1;s:11:" * template";N;s:9:" * layout";N;s:19:" * withComputedData";b:1;s:7:" * data";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:4:{s:9:"blueprint";s:4:"page";s:5:"title";s:9:"news aer]";s:4:"slug";s:9:"news-aer]";s:8:"template";s:25:"pages/news-aer]/news-aer]";}s:28:" * escapeWhenCastingToString";b:0;}s:14:" * supplements";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:14:" * initialPath";s:101:"/Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/content/collections/pages/news-aer.md";s:12:" * published";b:1;s:23:" * selectedQueryColumns";N;s:25:" * selectedQueryRelations";a:0:{}s:11:" * original";a:0:{}s:9:" * origin";N;s:7:" * slug";s:8:"news-aer";}