9999999999O:27:"Statamic\Entries\Collection":30:{s:9:" * handle";s:5:"pages";s:9:" * routes";s:19:"{parent_uri}/{slug}";s:41:" Statamic\Entries\Collection cachedRoutes";N;s:8:" * mount";N;s:8:" * title";s:5:"Pages";s:11:" * template";N;s:9:" * layout";N;s:8:" * sites";a:1:{i:0;s:7:"default";}s:12:" * propagate";b:1;s:13:" * blueprints";a:0:{}s:14:" * searchIndex";N;s:8:" * dated";b:0;s:12:" * sortField";N;s:16:" * sortDirection";N;s:12:" * revisions";b:0;s:12:" * positions";N;s:22:" * defaultPublishState";b:1;s:17:" * originBehavior";s:6:"select";s:21:" * futureDateBehavior";s:6:"public";s:19:" * pastDateBehavior";s:6:"public";s:12:" * structure";N;s:20:" * structureContents";a:1:{s:4:"root";b:1;}s:13:" * taxonomies";N;s:16:" * requiresSlugs";b:1;s:15:" * titleFormats";a:0:{}s:17:" * previewTargets";a:0:{}s:11:" * autosave";N;s:13:" * withEvents";b:1;s:10:" * cascade";O:29:"Illuminate\Support\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}s:14:" * initialPath";N;}