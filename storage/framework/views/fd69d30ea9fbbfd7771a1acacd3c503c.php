<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'eyebrow' => '',
    'title' => '',
    'description' => '',
    'image' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'eyebrow' => '',
    'title' => '',
    'description' => '',
    'image' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="PageTitle" data-module="ds-page-title" <?php echo e($attributes->merge(['class' => 'ds-page-title'])); ?>>
    <div class="relative z-20 overflow-hidden">
            <p class="ds-page-title__eyebrow"><?php echo $eyebrow ?? ''; ?></p>
            <h1 class="ds-page-title__title !leading-tight"><?php echo $title ?? ''; ?></h1>
            <?php echo e($slot); ?>

    </div>
    <div class="ds-page-title__image">
        <?php $__currentLoopData = Statamic::tag('glide:generate')->src($image ?? '')->width(1200)->height(600)->fit('crop_focal'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <img src="<?php echo e($image['url']); ?>" alt="">
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <div class="ds-page-title__overlay"></div>
</div><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/page-title/page-title.blade.php ENDPATH**/ ?>