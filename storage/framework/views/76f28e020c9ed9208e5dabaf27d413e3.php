<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'type' => 'primary',
    'icon_prepend' => null,
    'icon_append' => null,
    'image_append' => null,
    'href' => null,
    'alpineHref' => null,
    'target' => null, // Specify the Livewire target (e.g., a method or property)
    'window_target' => '_self'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'type' => 'primary',
    'icon_prepend' => null,
    'icon_append' => null,
    'image_append' => null,
    'href' => null,
    'alpineHref' => null,
    'target' => null, // Specify the Livewire target (e.g., a method or property)
    'window_target' => '_self'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<<?php echo e($href != null || $alpineHref != null ? 'a href=' . $href : 'button'); ?> 
    data-module="ds-button" 
    <?php echo e($attributes->merge(['class' => 'ds-button group ds-button--' . $type])); ?>

    <?php echo e($href != null || $alpineHref != null ? 'target=' . $window_target : ''); ?>

    wire:loading.attr="disabled"
>
    <?php if(isset($icon_prepend)): ?>
        <div class="ds-button__icon-container">
            <span class="relative z-10">
                <i class="<?php echo e($icon_prepend); ?>"></i>
            </span>
        </div>
    <?php endif; ?>
    <?php if(isset($image_append)): ?>
        <img src="<?php echo e($image_append); ?>" class="w-24 mr-2 -mt-0.5 ds-button__image-append" alt="">
    <?php endif; ?>

    <span class="relative z-10" wire:loading.remove <?php echo e($target ? "wire:target={$target}" : ''); ?>>
        <?php echo e($slot); ?>

    </span>

	<?php if($target !== null): ?>
    <span class="relative z-10 flex items-center space-x-2" wire:loading <?php echo e($target ? "wire:target={$target}" : ''); ?>>
        <i class="fas fa-spinner fa-spin"></i>
        <span>Please wait...</span>
    </span>
	<?php endif; ?>

    <?php if(isset($icon_append)): ?>
        <div class="ds-button__icon-container">
            <span class="relative z-10">
                <i class="<?php echo e($icon_append); ?>"></i>
            </span>
        </div>
    <?php endif; ?>
<<?php echo e($href != null || $alpineHref != null ? '/a' : '/button'); ?>>
<?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/button/button.blade.php ENDPATH**/ ?>