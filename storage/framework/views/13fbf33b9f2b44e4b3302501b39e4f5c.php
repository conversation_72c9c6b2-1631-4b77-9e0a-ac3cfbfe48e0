<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'item' => [],
    'glideWidth' => null,
    'glideHeight' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'item' => [],
    'glideWidth' => null,
    'glideHeight' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="Image" data-module="ds-cards--image" <?php echo e($attributes->merge(['class' => 'ds-cards--image'])); ?>>
    <?php if($glideWidth): ?>
    <?php $__currentLoopData = Statamic::tag('glide:generate')->src($item)->width($glideWidth)->height($glideHeight); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <img src="<?php echo e($image['url']); ?>" alt="<?php echo e($item['alt'] ?? ''); ?>" class="object-cover w-full h-full">
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php else: ?>
    <img src="<?php echo e($item['image'] ?? ''); ?>" alt="<?php echo e($item['alt'] ?? ''); ?>" class="object-cover w-full h-full">
    <?php endif; ?>
</div><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/card/image/image.blade.php ENDPATH**/ ?>