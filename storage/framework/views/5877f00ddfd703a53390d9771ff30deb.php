<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="Footer" data-module="ds-footer"
    <?php echo e($attributes->merge(['class' => 'ds-footer relative w-full relative overflow-hidden ds-section-padding-t leading-loose'])); ?>>
    <div class="ds-site-padding xl:flex xl:space-x-24">
        <div>
            <?php if (isset($component)) { $__componentOriginal436399e29d00ce6b8f47e38277d39536 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal436399e29d00ce6b8f47e38277d39536 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section-header.section-header','data' => ['title' => 'Contact','class' => 'dark !mb-6 text-left !justify-start !items-start']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Contact','class' => 'dark !mb-6 text-left !justify-start !items-start']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $attributes = $__attributesOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__attributesOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $component = $__componentOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__componentOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-12 gap-12 lg:auto-cols-min ds-section-padding-b w-full">
            <div class="lg:col-span-4">
                <h4>La Flamant</h4>
                <address class="not-italic leading-loose mb-4">
                    64 High Street<br> 
                    Lindfield<br> 
                    Haywards Heath<br> 
                    West Sussex<br> 
                    RH16 2HL 
                </address>
                <p class="flex items-center space-x-3"><i class="fa-regular fa-phone"></i><a href="tel:00441444523499" class="hover:text-yellow-150 duration-200">01444 523 499</a></p>
                <p class="flex items-center space-x-3"><i class="fa-regular fa-envelope"></i><a href="mailto:<EMAIL>" class="hover:text-yellow-150 duration-200"><EMAIL></a></p>
            </div>
            <div class="lg:col-span-5">
                <h4>Opening Hours</h4>
                <table class="footer-table w-full sm:w-auto md:w-full xl:w-auto">
                    <tbody>
                        <tr>
                            <td>Monday</td>
                            <td>Closed</td>
                        </tr>
                        <tr>
                            <td>Tuesday</td>
                            <td>Closed</td>
                        </tr>
                        <tr>
                            <td>Wednesday</td>
                            <td>12:00pm-11.00pm</td>
                        </tr>
                        <tr>
                            <td>Thursday</td>
                            <td>12:00pm-11.00pm</td>
                        </tr>
                        <tr>
                            <td>Friday</td>
                            <td>12:00pm-11.00pm</td>
                        </tr>
                        <tr>
                            <td>Saturday</td>
                            <td>12:00pm-11.00pm</td>
                        </tr>
                        <tr>
                            <td>Sunday</td>
                            <td>Closed</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="lg:col-span-3 lg:flex lg:flex-col lg:items-end lg:justify-start lg:flex-wrap">
                <h4>Follow us</h4>
                <ul class="social-links">
                    <li>
                        <a href="https://www.instagram.com/leflamantlindfield/" target="_blank"><i class="fa-brands fa-instagram"></i></a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="py-5">
        <div class="ds-site-padding md:flex justify-center md:justify-between text-sm text-neutral-200 font-sans">

            <p>&copy; <?php echo e(date('Y')); ?> Le Flamant. All rights reserved.</p>
            <div>
                <p>Site by <a href="https://www.danpatching.co.uk/" target="_blank" class="hover:text-white duration-200">Dan Patching Creative Design</a></p>
                <p>Photography by <a href="https://www.instagram.com/carlabarberphotography/" target="_blank" class="hover:text-white duration-200">Carla Barber</a></p>
            </div>

        </div>
    </div>
   
</div>
<?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/footer/footer.blade.php ENDPATH**/ ?>