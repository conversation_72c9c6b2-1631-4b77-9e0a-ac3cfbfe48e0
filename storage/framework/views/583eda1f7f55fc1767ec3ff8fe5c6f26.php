<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'eyebrow' => '',
    'title' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'eyebrow' => '',
    'title' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>
<div x-data="SectionHeader" data-module="ds-section-header" <?php echo e($attributes->merge(['class' => 'ds-section-header'])); ?>>
    <?php if($eyebrow): ?>
    <p class="ds-section-header__eyebrow"><?php echo e($eyebrow); ?></p>
    <?php endif; ?>
    <h2 class="ds-section-header__title !leading-none"><?php echo $title ?? ''; ?></h2>
    <p class="text-base md:text-lg lg:text-xl">
        <?php echo e($slot); ?>

    </p>
</div><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/section-header/section-header.blade.php ENDPATH**/ ?>