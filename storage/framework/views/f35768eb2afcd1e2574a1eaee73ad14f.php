<?php foreach ((['page']) as $__key => $__value) {
    $__consumeVariable = is_string($__key) ? $__key : $__value;
    $$__consumeVariable = is_string($__key) ? $__env->getConsumableComponentData($__key, $__value) : $__env->getConsumableComponentData($__value);
} ?>
<header x-data="Header" data-module="ds-header" <?php echo e($attributes->merge(['class' => 'ds-header'])); ?> x-cloak>
    <nav class="flex items-center justify-between ds-site-padding space-x-6">
        <div class="flex lg:flex-shrink-0">
            <a href="#home" class="-m-1.5 p-1.5 logo">
                <span class="sr-only">Le Flamant</span>
                <img class="master-logo" src="/img/le-flamant-wordmark.svg" alt="Le Flamant">
            </a>
        </div>

        <!-- Desktop Menu -->
        <ul class="hidden lg:flex lg:space-x-6 xl:space-x-10 desktop-menu">
            <li class="li group">
                <a href="#home" class="nav-link" data-target="home">Home
                <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#about" class="nav-link" data-target="about">
                    About Us
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#food" class="nav-link" data-target="food">
                    Our Food
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#drinks" class="nav-link" data-target="drinks">
                    Our Drinks
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#gallery" class="nav-link" data-target="gallery">
                    Gallery
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#contact" class="nav-link" data-target="contact">Contact
                <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
        </ul>

        <div class="flex items-center space-x-6">
            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['dataFancybox' => 'reservations','dataSrc' => '#reservation-modal','dataType' => 'inline','type' => 'secondary','class' => '!hidden md:!block header-btn']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data-fancybox' => 'reservations','data-src' => '#reservation-modal','data-type' => 'inline','type' => 'secondary','class' => '!hidden md:!block header-btn']); ?>Book a table <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginal7a41a60158cad7274b1183b16c28b914 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a41a60158cad7274b1183b16c28b914 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.hamburger-icon.hamburger-icon','data' => ['class' => 'lg:hidden']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('hamburger-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'lg:hidden']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a41a60158cad7274b1183b16c28b914)): ?>
<?php $attributes = $__attributesOriginal7a41a60158cad7274b1183b16c28b914; ?>
<?php unset($__attributesOriginal7a41a60158cad7274b1183b16c28b914); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a41a60158cad7274b1183b16c28b914)): ?>
<?php $component = $__componentOriginal7a41a60158cad7274b1183b16c28b914; ?>
<?php unset($__componentOriginal7a41a60158cad7274b1183b16c28b914); ?>
<?php endif; ?>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div @keydown.window.escape="closeMenu()" x-show="menuopen" x-cloak
    class="fixed inset-0 z-50 overflow-hidden h-dvh lg:!hidden" aria-labelledby="slide-over-title" x-ref="dialog"
    aria-modal="true">
        <div class="absolute inset-0 overflow-hidden">
            <div x-transition:enter="ease-in-out duration-500" x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-500"
                x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                x-description="Background overlay, show/hide based on slide-over state."
                class="absolute inset-0 transition-opacity bg-black bg-opacity-70" x-on:click="menuopen = false"
                aria-hidden="true">
            </div>
            <div class="absolute inset-y-0 right-0 flex max-w-full md:pl-10">
                <div x-show="menuopen"
                    x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
                    x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full"
                    class="w-screen max-w-full md:max-w-md"
                    x-description="Slide-over panel, show/hide based on slide-over state.">
                    <div class="flex flex-col h-full pt-6 pb-10 overflow-y-scroll shadow-xl bg-sage-500 md:pb-16">
                        <div class="relative flex-1 px-4 md:mt-24 sm:px-6">
                            <div class="absolute inset-0 flex flex-col justify-between px-4 sm:px-8">
                                <nav aria-label="Sidebar" class="flex flex-col h-full mobile-menu">
                                    <a href="#home" class="md:hidden">
                                        <span class="sr-only">Le Flamant</span>
                                        <img class="master-logo" src="/img/le-flamant-wordmark.svg" alt="Le Flamant">
                                    </a>
                                   <ul x-data="{ selected: null }">
                                        <li><a href="#home" class="group" @click="closeMenu()">Home</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#about" class="group" @click="closeMenu()">About Us</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#food" class="group" @click="closeMenu()">Our Food</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#drinks" class="group" @click="closeMenu()">Our Drinks</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#gallery" class="group" @click="closeMenu()">Gallery</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#contact" class="group" @click="closeMenu()">Contact</a></li>
                                        <div class="divider"></div>
                                    </ul>

                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/header/header.blade.php ENDPATH**/ ?>