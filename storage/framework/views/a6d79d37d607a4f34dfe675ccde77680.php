<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'slide' => [],
    'image' => null,
    'icon' => 'fa-light fa-circle-plus',
    'name' => '',
    'position' => '',
    'modal',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'slide' => [],
    'image' => null,
    'icon' => 'fa-light fa-circle-plus',
    'name' => '',
    'position' => '',
    'modal',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>
<?php
    if (!empty($slide)) {
        $image = $slide['image'];
        $icon = $slide['icon'] ?? 'fa-light fa-circle-plus';
        $name = $slide['name'] ?? '';
        $position = $slide['position'] ?? '';
         $modal = $slide['modal'];
    }
?>

<li x-data="Team-member" data-module="ds-team-member" <?php echo e($attributes->merge(['class' => 'ds-team-member group'])); ?>>
    <a data-fancybox data-src="#<?php echo e($modal); ?>" class="absolute h-full w-full">
        <div class="absolute inset-0 bg-sage-800 bg-opacity-25 opacity-100 group-hover:opacity-0 transition-opacity duration-300 z-10"></div>
        <p class="ds-team-member__position"><?php echo $position ?? ''; ?></p>
        <div class="relative overflow-hidden z-0">
            <img src="<?php echo e($image); ?>" alt="<?php echo e($name); ?>" class="ds-team-member__image">
        </div>
        <div class="ds-team-member__overlay">
            <h3 class="ds-team-member__name"><?php echo e($name); ?></h3>
            <p class="font-sans tracking-wider text-sm md:translate-y-24 text-yellow-150 opacity-0 group-hover:md:translate-y-20 group-hover:opacity-100 transition-all -mt-4 group-hover:mt-0 duration-200">VIEW PROFILE</p>
        </div>
    </a>
</li><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/card/team-member/team-member.blade.php ENDPATH**/ ?>