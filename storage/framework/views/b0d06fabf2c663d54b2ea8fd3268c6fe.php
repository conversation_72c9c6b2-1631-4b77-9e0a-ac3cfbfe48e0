<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="HamburgerIcon" data-module="ds-hamburger-icon" <?php echo e($attributes->merge(['class' => 'ds-hamburger-icon'])); ?>>
    <button class="z-[51] relative w-[29px] lg:w-[38px] h-[14px] lg:h-[17px] focus:outline-none group" x-on:click="menuopen = !menuopen">
      <span class="sr-only">Open main menu</span>
         <div class="absolute block w-[29px] lg:w-[38px] transform -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2">
            <span aria-hidden="true"
                  class="hamburger-line block absolute h-[2px] w-[29px] lg:w-[38px]  transform transition duration-300 ease-in-out"
                  :class="{'rotate-45 group-hover:text-neutral-300': menuopen,'-translate-y-1.5 group-hover:-translate-y-2  bg-white': !menuopen }">
            </span>
            <span aria-hidden="true"
                  class="hamburger-line block absolute h-[2px] w-[29px] lg:w-[38px] transform transition duration-300 ease-in-out"
                  :class="{'-rotate-45 group-hover:text-neutral-300': menuopen, 'translate-y-1.5 group-hover:translate-y-2  bg-white': !menuopen}">
            </span>
         </div>
    </button>
</div><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/hamburger-icon/hamburger-icon.blade.php ENDPATH**/ ?>