<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
	'componentClasses' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
	'componentClasses' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="FiftyFifty" data-module="ds-fifty-fifty" <?php echo e($attributes->merge(['class' => 'ds-fifty-fifty'])); ?>>
	<div class="ds-fifty-fifty__container <?php echo e($componentClasses); ?>">
		<div class="ds-fifty-fifty__left-block">
			<?php echo e($slot1); ?>

		</div>
		<div class="ds-fifty-fifty__right-block">
			<?php echo e($slot2); ?>

		</div>
	</div>
</div><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/components/fifty-fifty/fifty-fifty.blade.php ENDPATH**/ ?>