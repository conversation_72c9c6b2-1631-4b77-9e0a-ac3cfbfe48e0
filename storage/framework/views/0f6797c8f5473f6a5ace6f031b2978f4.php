<?php if (isset($component)) { $__componentOriginal1f9e5f64f242295036c059d9dc1c375c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1f9e5f64f242295036c059d9dc1c375c = $attributes; } ?>
<?php $component = App\View\Components\Layout::resolve(['context' => $__data] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div x-data="HomePage" class="ds-home">
        <section id="home" class="relative h-dvh bg-sage-800 overflow-hidden">
            <div class="absolute inset-0 bg-[radial-gradient(51%_100%,_rgba(0,_0,_0,_0%)_50%,_rgba(0,_0,_0,_71%)_100%)] z-10"></div>
            <img src="/img/hero-2.jpg" alt="Welcome to Le Flamant" class="object-cover h-full w-full animate-zoom-in">
            
            <div class="absolute inset-0 flex items-center justify-center ">
                <div class="text-center relative">
                    <div class="overflow-hidden">
                        <img src="/img/le-flamant-logo.svg" alt="Le Flamant" class="relative w-[220px] md:w-[335px] mx-auto mb-4 md:mb-6 z-20 animate-mask-fade-up opacity-0 transition-opacity">
                    </div>
                    <div class="absolute rounded-full inset-0 bg-black blur-2xl opacity-50"></div>
                    
                </div>
            </div>
        </section>

        <section id="about" class="ds-section-padding bg-yellow-150/40 relative">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <div class="grid md:grid-cols-3 gap-y-16">
                    <?php
                        $slides = [
                            'images' => [
                                ["image" => "/img/about/1.jpg", "alt" => "Le Flamant Pic 1"],
                                ["image" => "/img/about/2.jpg", "alt" => "Le Flamant Pic 2"],
                                ["image" => "/img/about/3.jpg", "alt" => "Le Flamant Pic 3"],
                                ["image" => "/img/about/4.jpg", "alt" => "Le Flamant Pic 4"],
                                ["image" => "/img/about/5.jpg", "alt" => "Le Flamant Pic 5"],
                                ["image" => "/img/about/6.jpg", "alt" => "Le Flamant Pic 6"],
                                ["image" => "/img/about/7.jpg", "alt" => "Le Flamant Pic 7"],
                                ["image" => "/img/about/8.jpg", "alt" => "Le Flamant Pic 8"]
                            ]
                        ];
                    ?>
                    <?php if (isset($component)) { $__componentOriginal85b998fcec609d68e61cda0142df35ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b998fcec609d68e61cda0142df35ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.carousel.card-based.card-based','data' => ['slides' => $slides['images'],'cardType' => 'card.image','autoPlay' => true,'class' => 'order-2 md:order-1 relative about-image-carousel aspect-3/4 overflow-hidden']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('carousel.card-based'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['slides' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($slides['images']),'cardType' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('card.image'),'autoPlay' => true,'class' => 'order-2 md:order-1 relative about-image-carousel aspect-3/4 overflow-hidden']); ?>
                        <div class="custom-nav">
                            <button class="button button--previous">
                                <i class="fa-thin fa-chevron-left"></i>
                            </button>
                            <button class="button button--next">
                                <i class="fa-thin fa-chevron-right"></i>
                            </button>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b998fcec609d68e61cda0142df35ee)): ?>
<?php $attributes = $__attributesOriginal85b998fcec609d68e61cda0142df35ee; ?>
<?php unset($__attributesOriginal85b998fcec609d68e61cda0142df35ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b998fcec609d68e61cda0142df35ee)): ?>
<?php $component = $__componentOriginal85b998fcec609d68e61cda0142df35ee; ?>
<?php unset($__componentOriginal85b998fcec609d68e61cda0142df35ee); ?>
<?php endif; ?>
                    
                    <div class="order-1 md:order-2 md:col-span-2 md:pl-16 lg:pl-20 md:pt-0 text-block">
                        <p class="font-sans tracking-wider uppercase mb-4 mt-0">About</p>
                        <h1 class="text-sage-800 text-5xl lg:text-6xl mb-6">A snug retreat with unforgettable flavours</h1>
                        <p>Nestled in the heart of Lindfield’s beautiful and historic high street, Le Flamant invites you to savour simple, quality food made for lingering conversations and stolen bites.</p>
                        <p>Choose from a menu of tempting plates, with every dish a delicious excuse to come together, relax, and indulge.</p> 
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['dataFancybox' => 'reservations','dataSrc' => '#reservation-modal','dataType' => 'inline','type' => 'primary','class' => 'mt-4 no-underline']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data-fancybox' => 'reservations','data-src' => '#reservation-modal','data-type' => 'inline','type' => 'primary','class' => 'mt-4 no-underline']); ?>Book a table <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
        <section class="ds-section-padding bg-sage-500 md:pb-40 lg:pb-48">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <?php if (isset($component)) { $__componentOriginal436399e29d00ce6b8f47e38277d39536 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal436399e29d00ce6b8f47e38277d39536 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section-header.section-header','data' => ['eyebrow' => 'Our Team','title' => 'The talent behind the taste','class' => 'dark text-center max-w-xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['eyebrow' => 'Our Team','title' => 'The talent behind the taste','class' => 'dark text-center max-w-xl']); ?>
                        <p>We're lucky to have one of the friendliest, most creative<br class="hidden md:block"> teams out there, committed to one thing only -<br class="hidden md:block"> bringing you experiences to remember.</p>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $attributes = $__attributesOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__attributesOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $component = $__componentOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__componentOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
                <ul role="list" class="grid md:grid-cols-3 gap-y-6 md:gap-x-2">
                    <?php
                        $data = [
                            'team' => [
                                ["modal" => "jackson-modal", "image" => "/img/team/jackson-heron.jpg", "name" => "Jackson Heron", "position" => "Head Chef"],
                                ["modal" => "felicity-modal", "image" => "/img/team/felicity-moseley.jpg", "name" => "Felicity Moseley", "position" => "Chef"],
                                ["modal" => "molly-modal", "image" => "/img/team/molly-maltby.jpg", "name" => "Molly Maltby", "position" => "Restaurant & Bar Manager"],
                            ]
                        ];
                    ?>
                    <?php $__currentLoopData = $data['team']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if (isset($component)) { $__componentOriginal251c8242746032fecf4ccad9478add80 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal251c8242746032fecf4ccad9478add80 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.team-member.team-member','data' => ['slide' => $slide]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card.team-member'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['slide' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($slide)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal251c8242746032fecf4ccad9478add80)): ?>
<?php $attributes = $__attributesOriginal251c8242746032fecf4ccad9478add80; ?>
<?php unset($__attributesOriginal251c8242746032fecf4ccad9478add80); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal251c8242746032fecf4ccad9478add80)): ?>
<?php $component = $__componentOriginal251c8242746032fecf4ccad9478add80; ?>
<?php unset($__componentOriginal251c8242746032fecf4ccad9478add80); ?>
<?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
                </div>
            </div>
        </section>

        <section id="food" class="bg-sage-800 dark food">
            <?php if (isset($component)) { $__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.fifty-fifty.fifty-fifty','data' => ['class' => 'ds-fifty-fifty--content-middle']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('fifty-fifty'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'ds-fifty-fifty--content-middle']); ?>
                 <?php $__env->slot('slot1', null, []); ?> 
                    <?php if (isset($component)) { $__componentOriginal22d447e3f5aafc93b8447b54b36ee789 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal22d447e3f5aafc93b8447b54b36ee789 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.image.image','data' => ['src' => '/img/our-food-3.jpg','alt' => 'Our Food','class' => 'aspect-square']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['src' => '/img/our-food-3.jpg','alt' => 'Our Food','class' => 'aspect-square']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal22d447e3f5aafc93b8447b54b36ee789)): ?>
<?php $attributes = $__attributesOriginal22d447e3f5aafc93b8447b54b36ee789; ?>
<?php unset($__attributesOriginal22d447e3f5aafc93b8447b54b36ee789); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal22d447e3f5aafc93b8447b54b36ee789)): ?>
<?php $component = $__componentOriginal22d447e3f5aafc93b8447b54b36ee789; ?>
<?php unset($__componentOriginal22d447e3f5aafc93b8447b54b36ee789); ?>
<?php endif; ?>
                 <?php $__env->endSlot(); ?>
                 <?php $__env->slot('slot2', null, []); ?> 
                    <div data-aos="fade-up" data-aos-duration="1000">
                        <div class="text-block">
                        <h2>Our Food</h2>
                        <p>At Le Flamant, dining is all about sharing. Enticing plates with bold, simple flavours and a constantly refreshed desserts menu. All created from the finest seasonal ingredients and made to be enjoyed together.</p>
                        <p>Please note we do not accept cash payments.</p>
                        </div>
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['href' => '/pdfs/le-flamant-food-menu.pdf','windowTarget' => '_blank','type' => 'secondary','class' => 'no-underline mt-5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '/pdfs/le-flamant-food-menu.pdf','window_target' => '_blank','type' => 'secondary','class' => 'no-underline mt-5']); ?>View Food Menu <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>  
                    </div>
                 <?php $__env->endSlot(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11)): ?>
<?php $attributes = $__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11; ?>
<?php unset($__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11)): ?>
<?php $component = $__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11; ?>
<?php unset($__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11); ?>
<?php endif; ?>
        </section>

        <section id="drinks" class="bg-sage-800 dark drinks">
            <?php if (isset($component)) { $__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.fifty-fifty.fifty-fifty','data' => ['class' => 'ds-fifty-fifty--content-middle ds-fifty-fifty--reverse']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('fifty-fifty'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'ds-fifty-fifty--content-middle ds-fifty-fifty--reverse']); ?>
                 <?php $__env->slot('slot1', null, []); ?> 
                    <?php if (isset($component)) { $__componentOriginal22d447e3f5aafc93b8447b54b36ee789 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal22d447e3f5aafc93b8447b54b36ee789 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.image.image','data' => ['src' => '/img/our-drinks-2.jpg','alt' => 'Our Drinks','class' => 'aspect-square']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('image'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['src' => '/img/our-drinks-2.jpg','alt' => 'Our Drinks','class' => 'aspect-square']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal22d447e3f5aafc93b8447b54b36ee789)): ?>
<?php $attributes = $__attributesOriginal22d447e3f5aafc93b8447b54b36ee789; ?>
<?php unset($__attributesOriginal22d447e3f5aafc93b8447b54b36ee789); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal22d447e3f5aafc93b8447b54b36ee789)): ?>
<?php $component = $__componentOriginal22d447e3f5aafc93b8447b54b36ee789; ?>
<?php unset($__componentOriginal22d447e3f5aafc93b8447b54b36ee789); ?>
<?php endif; ?>
                 <?php $__env->endSlot(); ?>
                 <?php $__env->slot('slot2', null, []); ?> 
                    <div data-aos="fade-up" data-aos-duration="1000">
                        <div class="text-block">
                            <h2>Our Drinks</h2>
                            <p>Catching up over a drink is one of life's great pleasures. So, we’re dedicated to making every sip memorable, partnering with passionate suppliers to ensure only the finest wines and spirits make the cut.</p>
                            <p>From award-winning local sparkling options, to intriguing global wines, and expertly crafted signature drinks, discover your new go-to indulgence.
                            </p>
                        </div>
                        <div class="lg:flex space-y-3 lg:space-x-4 lg:space-y-0 mt-10">
                            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['href' => '/pdfs/le-flamant-wine-list.pdf','windowTarget' => '_blank','type' => 'secondary','class' => 'no-underline']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '/pdfs/le-flamant-wine-list.pdf','window_target' => '_blank','type' => 'secondary','class' => 'no-underline']); ?>View Wine List <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['href' => '/pdfs/le-flamant-drinks-menu.pdf','windowTarget' => '_blank','type' => 'secondary','class' => 'mt-6 no-underline']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '/pdfs/le-flamant-drinks-menu.pdf','window_target' => '_blank','type' => 'secondary','class' => 'mt-6 no-underline']); ?>View Drinks Menu <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                        </div>
                    </div>
                 <?php $__env->endSlot(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11)): ?>
<?php $attributes = $__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11; ?>
<?php unset($__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11)): ?>
<?php $component = $__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11; ?>
<?php unset($__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11); ?>
<?php endif; ?>
        </section>

        <section id="gallery" class="overflow-hidden">
                <?php  
                   $slides = [
                            'images' => [
                                ["image" => "/img/gallery/1.jpg", "alt" => "Le Flamant Gallery Pic 1"],
                                ["image" => "/img/gallery/2.jpg", "alt" => "Le Flamant Gallery Pic 2"],
                                ["image" => "/img/gallery/3.jpg", "alt" => "Le Flamant Gallery Pic 3"],
                                ["image" => "/img/gallery/4.jpg", "alt" => "Le Flamant Gallery Pic 4"],
                                ["image" => "/img/gallery/5.jpg", "alt" => "Le Flamant Gallery Pic 5"],
                                ["image" => "/img/gallery/6.jpg", "alt" => "Le Flamant Gallery Pic 6"],
                                ["image" => "/img/gallery/7.jpg", "alt" => "Le Flamant Gallery Pic 7"],
                                ["image" => "/img/gallery/8.jpg", "alt" => "Le Flamant Gallery Pic 8"]
                            ]
                        ];
                    ?>
                    <?php if (isset($component)) { $__componentOriginal85b998fcec609d68e61cda0142df35ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b998fcec609d68e61cda0142df35ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.carousel.card-based.card-based','data' => ['slides' => $slides['images'],'cardType' => 'card.image','autoPlay' => true,'pageDots' => true,'class' => 'relative gallery-carousel aspect-4/3 md:aspect-video']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('carousel.card-based'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['slides' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($slides['images']),'cardType' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('card.image'),'autoPlay' => true,'pageDots' => true,'class' => 'relative gallery-carousel aspect-4/3 md:aspect-video']); ?>
                        <div class="custom-nav">
                            <button class="button button--previous">
                                <i class="fa-thin fa-chevron-left"></i>
                            </button>
                            <button class="button button--next">
                            <i class="fa-thin fa-chevron-right"></i>
                            </button>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b998fcec609d68e61cda0142df35ee)): ?>
<?php $attributes = $__attributesOriginal85b998fcec609d68e61cda0142df35ee; ?>
<?php unset($__attributesOriginal85b998fcec609d68e61cda0142df35ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b998fcec609d68e61cda0142df35ee)): ?>
<?php $component = $__componentOriginal85b998fcec609d68e61cda0142df35ee; ?>
<?php unset($__componentOriginal85b998fcec609d68e61cda0142df35ee); ?>
<?php endif; ?>
        </section>

        <section id="suppliers" class="ds-section-padding pb-32 md:pb-36 lg:pb-40 bg-sage-800 dark overflow-hidden">
            <div class="ds-site-padding" data-aos="fade-up" data-aos-duration="1000">
                <?php if (isset($component)) { $__componentOriginal436399e29d00ce6b8f47e38277d39536 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal436399e29d00ce6b8f47e38277d39536 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section-header.section-header','data' => ['eyebrow' => 'Our Suppliers','title' => 'Our wonderful makers and curators','class' => 'text-center max-w-2xl dark']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['eyebrow' => 'Our Suppliers','title' => 'Our wonderful makers and curators','class' => 'text-center max-w-2xl dark']); ?>
                    <p>We work with some of the UK and Sussex's finest suppliers –<br class="hidden md:block">  each dedicated to their craft...</p>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $attributes = $__attributesOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__attributesOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $component = $__componentOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__componentOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
            </div>
            <?php
                $slides = [
                    'suppliers' => [
                        ["link" => "https://hiddenspring.co.uk/", "title" => "Hidden Spring Vineyard" ,"image" => "/img/suppliers/hidden-spring-vineyard.webp"],
                        ["link" => "https://www.artelium.com/", "title" => "Artelium Wines" ,"image" => "/img/suppliers/artelium.jpg"],
                        ["link" => "https://www.sugruesouthdowns.com/", "title" => "Sugrue Wines" ,"image" => "/img/suppliers/sugrue.jpg"],
                        ["link" => "https://goldstonerum.com/", "title" => "Goldstone Rum" ,"image" => "/img/suppliers/goldstone-rum.jpg"],
                        ["link" => "https://asterleybros.com/", "title" => "Asterley Bros" ,"image" => "/img/suppliers/asterley-bros.jpg"],
                        ["link" => "https://www.sipello.com/", "title" => "Sipello" ,"image" => "/img/suppliers/sipello.jpg"],
                        ["link" => "https://www.harveyandbrockless.co.uk/", "title" => "Harvey & Brockless" ,"image" => "/img/suppliers/harvey-brockless.jpg"],
                        ["link" => "https://rittercourivaud.co.uk", "title" => "Ritter Courivaud" ,"image" => "/img/suppliers/ritter-courivaud.jpg"],
                        ["link" => "https://bluebellwines.com/", "title" => "Bluebell Vineyard Estates" ,"image" => "/img/suppliers/bluebell-vineyard.jpg"],        
                    ]
                ];
            ?>
            <?php if (isset($component)) { $__componentOriginal85b998fcec609d68e61cda0142df35ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal85b998fcec609d68e61cda0142df35ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.carousel.card-based.card-based','data' => ['slides' => $slides['suppliers'],'cardType' => 'card.project','contain' => 'false','wrapAround' => 'true','groupCells' => '1,2,3','autoPlay' => true,'pageDots' => true,'class' => 'suppliers-carousel','dataAos' => 'fade-up','dataAosDuration' => '1000']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('carousel.card-based'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['slides' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($slides['suppliers']),'cardType' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('card.project'),'contain' => 'false','wrapAround' => 'true','groupCells' => '1,2,3','autoPlay' => true,'pageDots' => true,'class' => 'suppliers-carousel','data-aos' => 'fade-up','data-aos-duration' => '1000']); ?>
               <div class="custom-nav">
                    <button class="button button--previous">
                        <i class="fa-thin fa-chevron-left"></i>
                    </button>
                    <button class="button button--next">
                        <i class="fa-thin fa-chevron-right"></i>
                    </button>
                </div>
                
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal85b998fcec609d68e61cda0142df35ee)): ?>
<?php $attributes = $__attributesOriginal85b998fcec609d68e61cda0142df35ee; ?>
<?php unset($__attributesOriginal85b998fcec609d68e61cda0142df35ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal85b998fcec609d68e61cda0142df35ee)): ?>
<?php $component = $__componentOriginal85b998fcec609d68e61cda0142df35ee; ?>
<?php unset($__componentOriginal85b998fcec609d68e61cda0142df35ee); ?>
<?php endif; ?>
        </section>

        <section id="contact" class="bg-white reviews">
            <?php if (isset($component)) { $__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.fifty-fifty.fifty-fifty','data' => ['class' => 'ds-fifty-fifty--content-middle']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('fifty-fifty'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'ds-fifty-fifty--content-middle']); ?>
                 <?php $__env->slot('slot1', null, []); ?> 
                    <div class="ds-fifty-fifty__content-container" data-aos="fade-up" data-aos-duration="1000">
                        <img src="/img/tripadvisor-logo.svg" alt="Tripadvisor" class="mb-6">
                        <?php if (isset($component)) { $__componentOriginal436399e29d00ce6b8f47e38277d39536 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal436399e29d00ce6b8f47e38277d39536 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section-header.section-header','data' => ['eyebrow' => 'Reviews','title' => 'A secret worth sharing...','class' => 'text-left !justify-start !items-start max-w-xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['eyebrow' => 'Reviews','title' => 'A secret worth sharing...','class' => 'text-left !justify-start !items-start max-w-xl']); ?>
                            <p>Enjoyed your experience with us? Please spread the love. It means the world to us when you do.</p>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $attributes = $__attributesOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__attributesOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $component = $__componentOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__componentOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['a' => true,'href' => 'https://www.tripadvisor.com/Restaurant_Review-g6908391-d32985649-Reviews-Le_Flamant-Lindfield_Haywards_Heath_West_Sussex_England.html','windowTarget' => '_blank','type' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['a' => true,'href' => 'https://www.tripadvisor.com/Restaurant_Review-g6908391-d32985649-Reviews-Le_Flamant-Lindfield_Haywards_Heath_West_Sussex_England.html','window_target' => '_blank','type' => 'primary']); ?>Leave a Review <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                    </div>
                 <?php $__env->endSlot(); ?>
                 <?php $__env->slot('slot2', null, []); ?> 
                    <div class="absolute inset-0 w-full h-full">
                        <img src="/img/vouchers-2.jpg" alt="Vouchers" class="object-cover w-full h-full grayscale mix-blend-multiply opacity-100">
                    </div>
                    <div class="ds-fifty-fifty__content-container" data-aos="fade-up" data-aos-duration="1000">
                        <?php if (isset($component)) { $__componentOriginal436399e29d00ce6b8f47e38277d39536 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal436399e29d00ce6b8f47e38277d39536 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section-header.section-header','data' => ['eyebrow' => 'Vouchers','title' => 'Because special people deserve to be celebrated','class' => 'dark text-left !justify-start !items-start max-w-xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['eyebrow' => 'Vouchers','title' => 'Because special people deserve to be celebrated','class' => 'dark text-left !justify-start !items-start max-w-xl']); ?>
                            <p>Looking to treat someone? Give them something to remember with a Le Flamant gift voucher. Then just keep everything crossed that they choose to bring you along too...</p>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $attributes = $__attributesOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__attributesOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $component = $__componentOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__componentOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.button','data' => ['a' => true,'href' => 'https://le-flamant.vouchercart.com/app/','windowTarget' => '_blank','type' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['a' => true,'href' => 'https://le-flamant.vouchercart.com/app/','window_target' => '_blank','type' => 'primary']); ?>Buy Now <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                    </div>
                 <?php $__env->endSlot(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11)): ?>
<?php $attributes = $__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11; ?>
<?php unset($__attributesOriginal06cf8c4639bccdaa07a45e0aa412eb11); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11)): ?>
<?php $component = $__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11; ?>
<?php unset($__componentOriginal06cf8c4639bccdaa07a45e0aa412eb11); ?>
<?php endif; ?>
        </section>

        <section class="bg-yellow-150 bg-opacity-25 ds-section-padding">
            <div class="ds-site-padding" data-aos="fade-up" data-aos-duration="1000">
                <?php if (isset($component)) { $__componentOriginal436399e29d00ce6b8f47e38277d39536 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal436399e29d00ce6b8f47e38277d39536 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section-header.section-header','data' => ['eyebrow' => 'Join','title' => 'Want to stay in touch?','class' => 'text-center max-w-3xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['eyebrow' => 'Join','title' => 'Want to stay in touch?','class' => 'text-center max-w-3xl']); ?>
                    <p>Sign up to our newsletter and follow us on <a href="https://www.instagram.com/leflamantlindfield/" target="_blank" class="underline text-sage-800 hover:text-sage-500 duration-200 underline-offset-2">Instagram</a> to be the first to hear about news, menu updates, events and treats.</p>
                <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $attributes = $__attributesOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__attributesOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal436399e29d00ce6b8f47e38277d39536)): ?>
<?php $component = $__componentOriginal436399e29d00ce6b8f47e38277d39536; ?>
<?php unset($__componentOriginal436399e29d00ce6b8f47e38277d39536); ?>
<?php endif; ?>
               <div id="mc_embed_shell" class="max-w-2xl mx-auto">
                    <div id="mc_embed_signup">
                        <form action="https://leflamant.us13.list-manage.com/subscribe/post?u=81eb00ad641b7a6be4d045519&amp;id=e670d17620&amp;f_id=005bc2e1f0" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate" target="_self" novalidate="">
                            <div id="mc_embed_signup_scroll">
                                <div class="mc-field-group">
                                    <label for="mce-EMAIL" class=" sr-only block text-sage-800 font-sans text-sm  tracking-wider mb-2 text-left uppercase">Email Address:</label>
                                    <div class="space-y-3 text-left sm:flex sm:space-x-2 sm:space-y-0 ">
                                        <input type="email" name="EMAIL" class="required email w-full border-0 py-4 px-4 text-lg text-neutral-600 font-sans shadow-sm ring-1 ring-inset ring-neutral-300 placeholder:text-neutral-400 focus:ring-2 focus:ring-inset focus:ring-[#e1e0ad] sm:leading-6" placeholder="Enter your email" id="mce-EMAIL" required="" value="">
                                        <input type="submit" name="subscribe" id="mc-embedded-subscribe" class="ds-button ds-button--primary !rounded-none" value="Subscribe">
                                    </div>
                                </div>
                                <div id="mce-responses" class="clear foot">
                                    <div class="response" id="mce-error-response" style="display: none;"></div>
                                    <div class="response" id="mce-success-response" style="display: none;"></div>
                                </div>
                                <div aria-hidden="true" style="position: absolute; left: -5000px;">
                                    /* real people should not fill this in and expect good things - do not remove this or risk form bot signups */
                                    <input type="text" name="b_81eb00ad641b7a6be4d045519_e670d17620" tabindex="-1" value="">
                                </div>
                            </div>
                        </form>
                    </div>
            </div>
                    
            </div>
        </section>
               

          
            <div id="jackson-modal" class="hidden max-w-4xl !bg-white border shadow">
                <div class="items-start md:flex">
                    <div class="flex items-start mb-8 md:w-5/12 md:mr-10 md:mb-0">
                        <div x-data="Image" data-module="ds-image" class="ds-image aspect-3/4 overflow-hidden">
                            <img src="/img/team/jackson-heron.jpg" :alt="title" class="object-cover w-full h-full">
                        </div>
                    </div>
                    <div class="md:w-7/12">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h4 class="text-4xl lg:text-5xl text-sage-800 font-semibold mb-2">Jackson Heron</h4>
                                <p class="font-sans tracking-wider text-sage-800 uppercase text-sm mb-1">Head Chef</p>
                            </div>
                        </div>
                        <div class="text-block prose-headings:mb-0 prose-ul:mt-0">
                           <p>A true talent, Jackson joins us from Kinsbrook Vineyard where he was Head Chef. Prior to that he was right hand man to Matt Gillan at Heritage and sous chef to Michael Bremner at Brighton’s beloved 64 degrees. He has also worked at renowned London restaurants The Typing Room and City Social.</p>
                            <p>Jackson has a passion for our neighbourhood restaurant vision: ‘I want everyone who comes here to fall in love with the food, to have that warm sense of welcome, and to leave wanting to come back again and again’.</p>
                        </div>
                    </div>
                </div>
            </div>
            

             
            <div id="felicity-modal" class="hidden max-w-4xl !bg-white border shadow">
                <div class="items-start md:flex">
                    <div class="flex items-start mb-8 md:w-5/12 md:mr-10 md:mb-0">
                        <div x-data="Image" data-module="ds-image" class="ds-image aspect-3/4 overflow-hidden">
                            <img src="/img/team/felicity-moseley.jpg" :alt="title" class="object-cover w-full h-full">
                        </div>
                    </div>
                    <div class="md:w-7/12">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h4 class="text-4xl lg:text-5xl text-sage-800 font-semibold mb-2">Felicity Moseley</h4>
                                <p class="font-sans tracking-wider text-sage-800 uppercase text-sm mb-1">Chef</p>
                            </div>
                        </div>
                        <div class="text-block prose-headings:mb-0 prose-ul:mt-0">
                           Fifi brings fresh creativity to the Le Flamant kitchen. Having honed her craft at Brighton institution, Riddle & Finns, she became the youngest chef in training ever hired at Michelin rated Hide in Piccadilly. She’s driven to make Le Flamant a true local gem – known for its imaginative dishes and vibrant atmosphere.</p>
                        </div>
                    </div>
                </div>
            </div>
            

            
            <div id="molly-modal" class="hidden max-w-4xl !bg-white border shadow">
                <div class="items-start md:flex">
                    <div class="flex items-start mb-8 md:w-5/12 md:mr-10 md:mb-0">
                        <div x-data="Image" data-module="ds-image" class="ds-image aspect-3/4 overflow-hidden">
                            <img src="/img/team/molly-maltby.jpg" :alt="title" class="object-cover w-full h-full">
                        </div>
                    </div>
                    <div class="md:w-7/12">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h4 class="text-4xl lg:text-5xl text-sage-800 font-semibold mb-2">Molly Maltby</h4>
                                <p class="font-sans tracking-wider text-sage-800 uppercase text-sm mb-1">Restaurant & Bar Manager</p>
                            </div>
                        </div>
                        <div class="text-block prose-headings:mb-0 prose-ul:mt-0">
                           Whether you’re after a perfectly crafted cocktail, or a carefully selected wine, Molly has you covered. With years of experience behind the bar of the beautiful Peacock Inn in Piltdown, Molly knows how to create a relaxed, welcoming space where you can truly unwind. </p>
                        </div>
                    </div>
                </div>
            </div>
            

            
            <div id="reservation-modal" class="hidden max-w-4xl !bg-white border shadow">
                    <script type='text/javascript' src='//www.opentable.co.uk/widget/reservation/loader?rid=375906&type=standard&theme=tall&color=1&dark=false&iframe=true&domain=couk&lang=en-GB&newtab=false&ot_source=Restaurant%20website&cfe=true'></script>
            </div>
            

    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1f9e5f64f242295036c059d9dc1c375c)): ?>
<?php $attributes = $__attributesOriginal1f9e5f64f242295036c059d9dc1c375c; ?>
<?php unset($__attributesOriginal1f9e5f64f242295036c059d9dc1c375c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1f9e5f64f242295036c059d9dc1c375c)): ?>
<?php $component = $__componentOriginal1f9e5f64f242295036c059d9dc1c375c; ?>
<?php unset($__componentOriginal1f9e5f64f242295036c059d9dc1c375c); ?>
<?php endif; ?><?php /**PATH /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/resources/views/pages/home/<USER>/ ?>