<?php extract(collect($attributes->getAttributes())->mapWithKeys(function ($value, $key) { return [Illuminate\Support\Str::camel(str_replace([':', '.'], ' ', $key)) => $value]; })->all(), EXTR_SKIP); ?>
@props(['item','glideWidth','glideHeight','class'])
<x-card.image :item="$item" :glideWidth="$glideWidth" :glideHeight="$glideHeight" :class="$class" >
<x-slot name="title" >{{ $title }}</x-slot>
{{ $slot ?? "" }}
</x-card.image>